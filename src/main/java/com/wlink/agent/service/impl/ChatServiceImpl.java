package com.wlink.agent.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.wlink.agent.client.DifyCompletionClient;
import com.wlink.agent.config.ExternalApiConfig;
import com.wlink.agent.constant.RedisKeyConstant;
import com.wlink.agent.dao.dto.DifyChatMessageDTO;
import com.wlink.agent.dao.mapper.AgentSoundI18nMapper;
import com.wlink.agent.dao.mapper.AgentSoundMapper;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiCreationContentMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiImageStyleMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiPointTransactionsMapper;
import com.wlink.agent.dao.mapper.AiUsersMapper;
import com.wlink.agent.dao.mapper.AiSessionConversationRecordMapper;
import com.wlink.agent.dao.mapper.AiSessionImageMapper;
import com.wlink.agent.dao.po.AgentSoundI18nPo;
import com.wlink.agent.dao.po.AgentSoundPo;
import com.wlink.agent.dao.po.AiChapterPo;
import com.wlink.agent.dao.po.AiCreationContentPo;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.dao.po.AiImageStylePo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiPointTransactionsPo;
import com.wlink.agent.dao.po.AiUsersPo;
import com.wlink.agent.dao.po.AiSessionConversationRecordPo;
import com.wlink.agent.dao.po.AiSessionImagePo;
import com.wlink.agent.manager.SseEmitterManager;
import com.wlink.agent.model.TextModerationResult;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.dto.SseCompletionEvent;
import com.wlink.agent.model.req.AiCreationSessionQueryReq;
import com.wlink.agent.model.req.ChatMessageRequest;
import com.wlink.agent.model.req.CreateConversationRequest;
import com.wlink.agent.model.req.DesignSaveReq;
import com.wlink.agent.model.req.UpdateSessionReq;
import com.wlink.agent.model.res.ConversationListResponse;
import com.wlink.agent.model.res.ConversationMessageItem;
import com.wlink.agent.model.res.ConversationMessageListResponse;
import com.wlink.agent.model.res.PromptOptimizeRes;
import com.wlink.agent.model.vo.AiCreationSessionInfoVo;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import com.wlink.agent.model.vo.AiCreationSessionVO;
import com.wlink.agent.model.vo.ConversationItem;

import com.wlink.agent.service.ChatService;
import com.wlink.agent.service.ContentModerationService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.UserContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;

import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;


import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ChatServiceImpl implements ChatService {

    @Value("${sse.heartbeat.interval:15}")
    private int heartbeatInterval; // 心跳间隔，默认15秒

    @Resource
    AiCreationSessionMapper aiCreationSessionMapper;
    @Resource
    AiImageStyleMapper aiImageStyleMapper;
    @Resource
    private WebClient webClient;
    @Resource
    private ExecutorService sseExecutor;

    @Resource
    private OkHttpClient okHttpClient;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    RedissonClient redissonClient;
    @Resource
    SseEmitterManager sseEmitterManager;
    @Resource
    private AiUsersMapper aiUsersMapper;
    @Resource
    private AiPointTransactionsMapper aiPointTransactionsMapper;
    @Resource
    private AiSessionConversationRecordMapper aiSessionConversationRecordMapper;
    @Resource
    private ExternalApiConfig externalApiConfig;
    // 心跳任务调度器
    private final ScheduledExecutorService heartbeatScheduler = Executors.newScheduledThreadPool(1);
    @Autowired
    private AiImageTaskQueueMapper aiImageTaskQueueMapper;
    @Resource
    private AgentSoundMapper agentSoundMapper;
    @Resource
    ContentModerationService contentModerationService;
    @Resource
    private AgentSoundI18nMapper agentSoundI18nMapper;
    @Resource
    private AiCreationContentMapper aiCreationContentMapper;
    @Resource
    private DifyCompletionClient difyCompletionClient;
    @Autowired
    private AiChapterMapper aiChapterMapper;
    @Resource
    private AiSessionImageMapper aiSessionImageMapper;

    @Override
    public ConversationItem createConversation(
            CreateConversationRequest request) {
        log.info("ChatService createConversation request:{}", JSON.toJSONString(request));
        SimpleUserInfo simpleUserInfo = UserContext.getUser();
        TextModerationResult textModerationResult = contentModerationService.moderateText(request.getPrompt());
        if (!textModerationResult.isNormal()) {
            //prompt包含敏感词
            throw new BizException("prompt包含敏感词，请修改后重试");
        }
        String sessionId = IdUtil.getSnowflakeNextIdStr();
        ConversationItem item = new ConversationItem();
        AiCreationSessionPo aiCreationSessionPo = new AiCreationSessionPo();
        aiCreationSessionPo.setUserId(simpleUserInfo.getUserId());
        aiCreationSessionPo.setPrompt(request.getPrompt());
        aiCreationSessionPo.setSoundId(request.getSoundId());
        aiCreationSessionPo.setSessionId(sessionId);
        if (null != request.getImageStyleId()) {
            AiImageStylePo aiImageStylePo = aiImageStyleMapper.selectById(request.getImageStyleId());
            if (Objects.isNull(aiImageStylePo)) {
                throw new BizException("Invalid image style ID");
            }
        }
        aiCreationSessionPo.setImageStyleId(request.getImageStyleId());
        aiCreationSessionPo.setImageSize(request.getImageSize());
        aiCreationSessionPo.setImageModel(request.getImageModel());
        aiCreationSessionMapper.insert(aiCreationSessionPo);

        // 保存图片URL到关联表
        if (!CollectionUtils.isEmpty(request.getImageUrls())) {
            List<String> imageUrls = request.getImageUrls();
            List<AiSessionImagePo> sessionImageList = new ArrayList<>();
            for (int i = 0; i < imageUrls.size(); i++) {
                AiSessionImagePo sessionImagePo = new AiSessionImagePo();
                sessionImagePo.setSessionId(sessionId);
                sessionImagePo.setImageUrl(imageUrls.get(i));
                sessionImagePo.setSortOrder(i + 1); // 排序从1开始
                sessionImageList.add(sessionImagePo);
            }
            // 批量插入
            aiSessionImageMapper.insertBatch(sessionImageList);
            log.info("Batch saved {} images for session: {}", imageUrls.size(), sessionId);
        }

        item.setSessionId(sessionId);
        log.info("ChatService createConversation response:{}", JSON.toJSONString(item));
        return item;
    }


    @Override
    public SseEmitter handleChatMessage(ChatMessageRequest request) {
        String username = UserContext.getUser().getUserId();
        //String username = "huyidao";
        SseEmitter emitter = new SseEmitter(Duration.ofHours(1).toMillis());
        log.info("Received chat request: {}", request);
        sseEmitterManager.register(request.getConversation_id(), emitter);
        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, request.getConversation_id()));
        if (aiCreationSessionPo == null) {
            emitter.completeWithError(new RuntimeException("Invalid session ID"));
            return emitter;
        }
        log.info("AiCreationSessionPo aiCreationSessionPo: {}", JSON.toJSONString(aiCreationSessionPo));
        // 检查用户积分余额
        SimpleUserInfo userInfo = UserContext.getUser();
        AiUsersPo userPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>()
                .eq(AiUsersPo::getUserId, userInfo.getUserId()));
        if (userPo == null || userPo.getPoints() == null || userPo.getPoints() <= 0) {
            try {
                SseCompletionEvent errorEvent = SseCompletionEvent.error("积分不足，无法继续生成，请充值后再试");
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data(JSON.toJSONString(errorEvent), MediaType.APPLICATION_JSON));
                emitter.complete();
            } catch (IOException e) {
                log.error("Failed to send error message for insufficient points", e);
                emitter.completeWithError(e);
            }
            return emitter;
        }
        Long count = aiCreationContentMapper.selectCount(new LambdaQueryWrapper<AiCreationContentPo>()
                .eq(AiCreationContentPo::getSessionId, aiCreationSessionPo.getSessionId()));
        if (Objects.equals(request.getQuery(), "开始创作") && count > 0) {
            emitter.complete();
        }
        // 检查高token使用标识，如果存在则清空conversationId
        String highTokenFlagKey = RedisKeyConstant.getKey(RedisKeyConstant.Chat.HIGH_TOKEN_FLAG, aiCreationSessionPo.getSessionId());
        RBucket<Boolean> highTokenFlag = redissonClient.getBucket(highTokenFlagKey);
        RBucket<Boolean> bucket = redissonClient.getBucket(RedisKeyConstant.Chat.DIFY_CONVERSATION + aiCreationSessionPo.getSessionId());
        if (highTokenFlag.isExists()) {
            log.info("检测到高token使用标识，清空会话上下文，sessionId: {}", aiCreationSessionPo.getSessionId());
            AiCreationSessionPo creationSessionPo = new AiCreationSessionPo();
            creationSessionPo.setId(aiCreationSessionPo.getId());
            creationSessionPo.setConversationId("");
            creationSessionPo.setUpdateTime(new Date());
            aiCreationSessionMapper.updateById(creationSessionPo);
            // 删除标识
            highTokenFlag.delete();
            if (bucket.isExists()) {
                bucket.delete();
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("TaskID", aiCreationSessionPo.getSessionId());
        request.setInputs(jsonObject);
        if (Objects.equals(request.getQuery(), "开始创作") && StringUtils.isBlank(aiCreationSessionPo.getConversationId())) {
            // 查询ai_session_image表，获取关联的图片，然后set到files字段里面
            List<AiSessionImagePo> sessionImages = aiSessionImageMapper.selectList(
                new LambdaQueryWrapper<AiSessionImagePo>()
                    .eq(AiSessionImagePo::getSessionId, aiCreationSessionPo.getSessionId())
                    .eq(AiSessionImagePo::getDelFlag, 0)
                    .orderByAsc(AiSessionImagePo::getSortOrder)
            );

            if (!sessionImages.isEmpty()) {
                List<ChatMessageRequest.FileInfo> files = sessionImages.stream()
                    .map(sessionImage -> {
                        ChatMessageRequest.FileInfo fileInfo = new ChatMessageRequest.FileInfo();
                        fileInfo.setType("image");
                        fileInfo.setTransferMethod("remote_url");
                        fileInfo.setUrl(MediaUrlPrefixUtil.getMediaUrl(sessionImage.getImageUrl()));
                        return fileInfo;
                    })
                    .collect(Collectors.toList());
                request.setFiles(files);
                log.info("设置会话图片到files字段，共{}张图片", files.size());
            }
        }
        if (StringUtils.isNotBlank(aiCreationSessionPo.getConversationId())) {
            request.setConversation_id(aiCreationSessionPo.getConversationId());
        } else {
            request.setConversation_id("");
        }
        request.setUser(username);

        // 清理请求中的'None'值
        sanitizeChatMessageRequest(request);

        // 详细记录请求对象的每个字段
        log.info("=== ChatMessageRequest详细信息 ===");
        log.info("query: [{}]", request.getQuery());
        log.info("response_mode: [{}]", request.getResponse_mode());
        log.info("conversation_id: [{}]", request.getConversation_id());
        log.info("user: [{}]", request.getUser());
        log.info("inputs: [{}]", request.getInputs());
        if (request.getFiles() != null) {
            log.info("files count: {}", request.getFiles().size());
            for (int i = 0; i < request.getFiles().size(); i++) {
                ChatMessageRequest.FileInfo file = request.getFiles().get(i);
                log.info("file[{}] - type: [{}], transferMethod: [{}], url: [{}]",
                        i, file.getType(), file.getTransferMethod(), file.getUrl());
            }
        } else {
            log.info("files: null");
        }
        log.info("=== 序列化后的JSON ===");
        String jsonForLog = JSON.toJSONString(request);
        log.info("JSON: {}", jsonForLog);
        log.info("=== 检查'None'值 ===");
        if (jsonForLog.contains("\"None\"") || jsonForLog.contains("'None'")) {
            log.error("发现'None'值在JSON中: {}", jsonForLog);
        } else {
            log.info("JSON中未发现'None'值");
        }

        // 设置并启动心跳任务
        AtomicReference<ScheduledFuture<?>> heartbeatTask = new AtomicReference<>();
        startHeartbeat(emitter, heartbeatTask);

        // 标记是否收到任何数据
        AtomicBoolean hasReceivedData = new AtomicBoolean(false);

        sseExecutor.execute(() -> { // Using standard lambda for Runnable
            try {
                log.info("Calling external API for chat messages");

                // 手动构建安全的JSON请求体
                String requestBodyJson = buildSafeJsonRequest(request);
                log.info("Built safe JSON request body: {}", requestBodyJson);

                Flux<String> eventStream = webClient.post()
                        .uri(externalApiConfig.getChatUrl() + "/chat-messages")
                        .header(HttpHeaders.AUTHORIZATION, "Bearer " + externalApiConfig.getChatApiKey())
                        .header(HttpHeaders.CONTENT_TYPE, "application/json")
                        .bodyValue(requestBodyJson)
                        .exchangeToFlux(clientResponse -> {
                            if (clientResponse.statusCode().is4xxClientError() || clientResponse.statusCode().is5xxServerError()) {
                                // 获取详细的错误信息
                                return clientResponse.bodyToMono(String.class)
                                    .doOnNext(errorBody -> {
                                        log.error("External API returned error. Status: {}, Headers: {}, Body: {}",
                                                clientResponse.statusCode(),
                                                clientResponse.headers().asHttpHeaders(),
                                                errorBody);

                                        // 记录请求信息用于调试
                                        log.error("Request details - URL: {}, Headers: Authorization=Bearer [MASKED], Body: {}",
                                                externalApiConfig.getChatUrl() + "/chat-messages",
                                                JSON.toJSONString(request));
                                    })
                                    .flatMapMany(errorBody -> {
                                        String errorMessage = String.format("External API error: %s - %s",
                                                clientResponse.statusCode(), errorBody);
                                        return Flux.error(new RuntimeException(errorMessage));
                                    })
                                    .onErrorResume(bodyError -> {
                                        // 如果无法读取响应体，至少记录状态码
                                        log.error("External API returned error. Status: {}, Headers: {}, Failed to read body: {}",
                                                clientResponse.statusCode(),
                                                clientResponse.headers().asHttpHeaders(),
                                                bodyError.getMessage());

                                        String errorMessage = String.format("External API error: %s - Unable to read response body",
                                                clientResponse.statusCode());
                                        return Flux.error(new RuntimeException(errorMessage));
                                    });
                            } else {
                                // 正常响应，处理数据流
                                return clientResponse.bodyToFlux(DataBuffer.class);
                            }
                        })
                        .cast(DataBuffer.class)
                        .map(dataBuffer -> {
                            byte[] bytes = new byte[dataBuffer.readableByteCount()];
                            dataBuffer.read(bytes);
                            org.springframework.core.io.buffer.DataBufferUtils.release(dataBuffer); // 确保释放 buffer
                            String data = new String(bytes, StandardCharsets.UTF_8);
                            hasReceivedData.set(true); // 标记已收到数据
                            return data;
                        })
                        .retry(3) // 简单重试3次
                        .onErrorResume(e -> {
                            log.error("Error in eventStream processing: {}", e.getMessage(), e);

                            // 如果已经收到并处理了部分数据，那么我们可以优雅地关闭流而不是报错
                            if (hasReceivedData.get()) {
                                log.info("Some data was already processed, completing gracefully despite error");
                                return Flux.empty();
                            }

                            // 没有接收到任何数据，通知客户端出错
                            try {
                                SseCompletionEvent errorEvent = SseCompletionEvent.error("API连接中断，请稍后重试");
                                emitter.send(SseEmitter.event()
                                        .name("error")
                                        .data(JSON.toJSONString(errorEvent), MediaType.APPLICATION_JSON));
                            } catch (IOException ioException) {
                                log.error("Failed to send error message to client", ioException);
                            }

                            emitter.completeWithError(e);
                            return Flux.empty();
                        });

                // 用于累积不完整数据片段的缓冲区
                StringBuilder dataBuffer = new StringBuilder();

                eventStream.subscribe(
                        rawData -> {
                            try {
                                log.info("Received raw SSE data: {}", rawData);
                                // 如果是 ping 消息，直接跳过处理
                                if (rawData.contains("event: ping")) {
                                    log.debug("Ignored ping event.");
                                    return;
                                }
                                // 将新收到的数据添加到缓冲区
                                dataBuffer.append(rawData);

                                // 尝试从缓冲区中提取完整的消息
                                processBufferedData(dataBuffer, aiCreationSessionPo, bucket, emitter);

                            } catch (Exception e) {
                                // 捕获总体处理异常，记录日志但不中断流
                                log.error("Error processing SSE data: {}", e.getMessage(), e);
                                // 只有在无法继续处理时才完全中断
                                if (e instanceof IllegalStateException) {
                                    stopHeartbeat(heartbeatTask.get());
                                    emitter.completeWithError(e);
                                }
                            }
                        },
                        error -> {
                            log.error("Error from external API: {}", error.getMessage(), error);

                            // 发送一个最终消息，表明连接已结束
                            try {
                                SseCompletionEvent closeEvent = SseCompletionEvent.connectionClosed("外部API连接已关闭");
                                emitter.send(SseEmitter.event()
                                        .name("connection_closed")
                                        .data(JSON.toJSONString(closeEvent), MediaType.APPLICATION_JSON));
                            } catch (IOException ioException) {
                                log.error("Failed to send connection closed message", ioException);
                            } finally {
                                // 无论如何都确保完成 emitter
                                try {
                                    stopHeartbeat(heartbeatTask.get());
                                    // 如果已经有数据被处理，我们可以选择完成而不是报错
                                    if (hasReceivedData.get()) {
                                        log.info("Completing SSE stream after partial data processing despite error");
                                        emitter.complete();
                                    } else {
                                        emitter.completeWithError(error);
                                    }
                                } catch (Exception e) {
                                    log.error("Error completing emitter", e);
                                }
                            }
                        },
                        () -> {
                            log.info("External API stream completed.");

                            // 处理可能剩余在缓冲区中的数据
                            if (dataBuffer.length() > 0) {
                                try {
                                    processBufferedData(dataBuffer, aiCreationSessionPo, bucket, emitter);
                                } catch (Exception e) {
                                    log.error("Error processing remaining data: {}", e.getMessage(), e);
                                }
                            }

                            // 发送一个最终消息，表明连接已正常结束
                            try {
//                                SseCompletionEvent completeEvent = SseCompletionEvent.completion("数据接收完成");
//                                emitter.send(SseEmitter.event()
//                                        .name("completion")
//                                        .data(JSON.toJSONString(completeEvent), MediaType.APPLICATION_JSON));
                                log.info("Sent completion message to client");
                            } catch (Exception ioException) {
                                log.error("Failed to send completion message", ioException);
                            } finally {
                                // 无论如何都确保停止心跳并完成 emitter
                                stopHeartbeat(heartbeatTask.get());
                                try {
                                    emitter.complete();
                                    log.info("Successfully completed SSE emitter");
                                } catch (Exception e) {
                                    log.error("Error completing emitter", e);
                                }
                            }
                        }
                );

                emitter.onCompletion(() -> {
                    log.info("SSE Emitter completed.");
                    stopHeartbeat(heartbeatTask.get());
                });
                emitter.onTimeout(() -> {
                    log.warn("SSE Emitter timed out.");
                    stopHeartbeat(heartbeatTask.get());
                    emitter.complete();
                });
                emitter.onError(e -> {
                    log.error("SSE Emitter error: {}", e.getMessage(), e);
                    stopHeartbeat(heartbeatTask.get());
                });

            } catch (Exception e) {
                log.error("Error handling chat message: {}", e.getMessage(), e);
                stopHeartbeat(heartbeatTask.get());
                emitter.completeWithError(e);
            }
        });

        log.info("Returning SseEmitter.");
        return emitter;
    }


    private void processBufferedData(StringBuilder buffer, AiCreationSessionPo aiCreationSessionPo,
                                     RBucket<Boolean> bucket, SseEmitter emitter) {
        String bufferContent = buffer.toString();

        // 按行分割数据，处理可能包含多个事件的情况
        String[] lines = bufferContent.split("\n");

        // 记录已处理的字符数
        int processedLength = 0;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];

            // 跳过空行
            if (line.trim().isEmpty()) {
                processedLength += line.length() + 1; // +1 for the newline
                continue;
            }

            // 处理 "data:" 前缀
            String processedData = line;
            if (processedData.startsWith("data:")) {
                processedData = processedData.substring(5).trim();
            }

            // 尝试解析为JSON
            try {
                DifyChatMessageDTO dto = JSON.parseObject(processedData, DifyChatMessageDTO.class);

                // JSON解析成功，说明是完整的消息
                log.info("Successfully parsed SSE data: {}", processedData);

                // 保存会话ID
                try {
                    if (null != dto && StringUtils.isNotBlank(dto.getConversationId())
                            && StringUtils.isBlank(aiCreationSessionPo.getConversationId())
                            && bucket.get() == null) {
                        AiCreationSessionPo creationSessionPo = new AiCreationSessionPo();
                        creationSessionPo.setId(aiCreationSessionPo.getId());
                        creationSessionPo.setConversationId(dto.getConversationId());
                        creationSessionPo.setTaskId(dto.getTaskId());
                        creationSessionPo.setUpdateTime(new Date());
                        aiCreationSessionMapper.updateById(creationSessionPo);
                        bucket.set(true, 1, TimeUnit.DAYS);

                        // 保存sessionId和conversationId的关系记录
                        AiSessionConversationRecordPo recordPo = new AiSessionConversationRecordPo();
                        recordPo.setSessionId(aiCreationSessionPo.getSessionId());
                        recordPo.setConversationId(dto.getConversationId());
                        recordPo.setUserId(aiCreationSessionPo.getUserId());
                        recordPo.setCreateTime(new Date());
                        recordPo.setUpdateTime(new Date());
                        recordPo.setDelFlag(0);
                        aiSessionConversationRecordMapper.insert(recordPo);
                        log.info("保存会话关系记录: sessionId={}, conversationId={}",
                                aiCreationSessionPo.getSessionId(), dto.getConversationId());
                    }

                    // 处理"message_end"事件，计算并扣除积分
                    if (null != dto && "message_end".equals(dto.getEvent()) && dto.getMetadata() != null
                            && dto.getMetadata().getUsage() != null) {
                        DifyChatMessageDTO.MetadataDTO.UsageDTO usage = dto.getMetadata().getUsage();
                        Integer promptTokens = usage.getPromptTokens() != null ? usage.getPromptTokens() : 0;
                        Integer completionTokens = usage.getCompletionTokens() != null ? usage.getCompletionTokens() : 0;
                        Integer totalTokens = promptTokens + completionTokens;

                        // 检查promptTokens是否超过100000，如果是则设置Redis标识
                        if (promptTokens > 100000) {
                            String highTokenFlagKey = RedisKeyConstant.getKey(RedisKeyConstant.Chat.HIGH_TOKEN_FLAG, aiCreationSessionPo.getSessionId());
                            RBucket<Boolean> highTokenFlag = redissonClient.getBucket(highTokenFlagKey);
                            highTokenFlag.set(true, 2, TimeUnit.HOURS); // 2小时超时
                            log.info("设置高token使用标识，sessionId: {}, promptTokens: {}",
                                    aiCreationSessionPo.getSessionId(), promptTokens);
                        }
                        AiCreationSessionPo sessionPo = new AiCreationSessionPo();
                        sessionPo.setId(aiCreationSessionPo.getId());
                        // 更新会话记录的token使用量
                        sessionPo.setTokenUsage(totalTokens);
                        sessionPo.setUpdateTime(new Date());
                        aiCreationSessionMapper.updateById(sessionPo);

                        // 计算需要扣除的积分（1积分 = 1000 token）
                        int pointsToDeduct = (int) Math.ceil(totalTokens / 1000.0);
                        if (pointsToDeduct > 0) {
                            // 获取用户信息
                            AiUsersPo userPo = aiUsersMapper.selectOne(new LambdaQueryWrapper<AiUsersPo>()
                                    .eq(AiUsersPo::getUserId, aiCreationSessionPo.getUserId()));

                            if (userPo != null && userPo.getPoints() != null) {
                                // 扣除积分
                                int remainingPoints = Math.max(0, userPo.getPoints() - pointsToDeduct);
                                userPo.setPoints(remainingPoints);
                                userPo.setUpdateTime(new Date());
                                aiUsersMapper.updateById(userPo);

                                // 记录积分交易
                                AiPointTransactionsPo transaction = new AiPointTransactionsPo();
                                transaction.setUserId(aiCreationSessionPo.getUserId());
                                transaction.setPoints(-pointsToDeduct); // 负数表示减少
                                transaction.setBalance(remainingPoints);
                                transaction.setType(3); // 3-会话消耗
                                transaction.setReferenceId(aiCreationSessionPo.getSessionId());
                                transaction.setDescription("AI会话消耗积分，使用" + totalTokens + "个token");
                                transaction.setCreateTime(new Date());
                                transaction.setUpdateTime(new Date());
                                transaction.setDelFlag(0);
                                aiPointTransactionsMapper.insert(transaction);

                                log.info("Deducted {} points for user {}, tokens used: {}, remaining points: {}",
                                        pointsToDeduct, aiCreationSessionPo.getUserId(), totalTokens, remainingPoints);
                                usage.setPointsToDeduct(pointsToDeduct);
                                usage.setRemainingPoints(userPo.getPoints());
                                DifyChatMessageDTO.MetadataDTO metadata = dto.getMetadata();
                                metadata.setUsage(usage);
                                dto.setMetadata(metadata);
                                processedData = objectMapper.writeValueAsString(dto);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("Error processing event data: {}", e.getMessage(), e);
                }

                // 发送给客户端
                try {
                    emitter.send(SseEmitter.event().data(processedData, MediaType.APPLICATION_JSON));
                } catch (IOException e) {
                    log.error("Error sending SSE data: {}", e.getMessage(), e);
                    emitter.completeWithError(e);
                }

                // 更新已处理的字符数
                processedLength += line.length() + 1; // +1 for the newline

            } catch (Exception e) {
                // JSON解析失败，可能是不完整的消息
                log.debug("Incomplete JSON data, buffering: {}", line);
                break; // 退出循环，保留剩余内容在缓冲区
            }
        }

        // 从缓冲区中移除已处理的内容
        if (processedLength > 0) {
            buffer.delete(0, Math.min(processedLength, buffer.length()));
        }
    }

    @Override
    public ConversationListResponse getConversationList(int limit) {
        log.info("getConversationList called with limit={}", limit);
        SimpleUserInfo simpleUserInfo = UserContext.getUser();
        try {
            String url = externalApiConfig.getChatUrl() + "/conversations" +
                    "?user=" + simpleUserInfo.getUserId() +
                    "&limit=" + limit;

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + externalApiConfig.getChatApiKey())
                    .header("Accept", "application/json")
                    .get()
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    throw new IOException("Response body is null");
                }

                String responseString = responseBody.string();
                return JSON.parseObject(responseString, ConversationListResponse.class);
            }
        } catch (Exception e) {
            log.error("Error fetching conversation list: {}", e.getMessage(), e);
            throw new BizException("Failed to fetch conversation list");
        }
    }

    @Override
    public void deleteConversation(String conversationId) {
        log.info("deleteConversation called with conversationId={}", conversationId);
        // 先获取会话信息
        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, conversationId)
                .eq(AiCreationSessionPo::getUserId, UserContext.getUser().getUserId()));
        if (aiCreationSessionPo == null) {
            return;
        }
        // 删除本地数据库记录
        aiCreationSessionMapper.delete(new LambdaQueryWrapper<AiCreationSessionPo>()
                .eq(AiCreationSessionPo::getSessionId, conversationId));

        if (StringUtils.isNotBlank(aiCreationSessionPo.getConversationId())) {
            try {
                // 调用外部接口删除会话
                String url = externalApiConfig.getChatUrl() + "/conversations/" + aiCreationSessionPo.getConversationId();
                log.info("Deleting conversation from external API: {}", url);

                Request request = new Request.Builder()
                        .url(url)
                        .header("Authorization", "Bearer " + externalApiConfig.getChatApiKey())
                        .delete()
                        .build();
                try (Response response = okHttpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        log.error("Failed to delete conversation from external API: {}", response);
                    } else {
                        ResponseBody responseBody = response.body();
                        if (responseBody != null) {
                            String responseString = responseBody.string();
                            log.info("Delete conversation response: {}", responseString);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error deleting conversation from external API: {}", e.getMessage(), e);
            }
            // 删除Redis缓存
            redissonClient.getBucket(RedisKeyConstant.Chat.DIFY_CONVERSATION + conversationId).delete();
        }
    }

    @Override
    public ConversationMessageListResponse getConversationMessages(String conversationId, int limit) {
        log.info("getConversationMessages called with conversationId={}, limit={}", conversationId, limit);
        try {
            SimpleUserInfo simpleUserInfo = UserContext.getUser();
            AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(new LambdaQueryWrapper<AiCreationSessionPo>()
                    .eq(AiCreationSessionPo::getSessionId, conversationId));
            if (aiCreationSessionPo == null || StringUtils.isBlank(aiCreationSessionPo.getConversationId())) {
                // 发送错误响应
                ConversationMessageListResponse conversationMessageListResponse = new ConversationMessageListResponse();
                conversationMessageListResponse.setLimit(limit);
                conversationMessageListResponse.setHas_more(false);
                List<ConversationMessageItem> conversationMessageItemList = new ArrayList<>();
                if (aiCreationSessionPo != null && StringUtils.isNotBlank(aiCreationSessionPo.getTaskId())) {
                    ConversationMessageItem conversationMessageItem = new ConversationMessageItem();
                    conversationMessageItem.setQuery(aiCreationSessionPo.getPrompt());
                    conversationMessageItemList.add(conversationMessageItem);
                    ConversationMessageItem conversationMessageItem1 = new ConversationMessageItem();
                    conversationMessageItem1.setQuery("上下文已清空，现在可以继续创作内容。");
                    conversationMessageItemList.add(conversationMessageItem1);
                }
                conversationMessageListResponse.setData(conversationMessageItemList);
                return conversationMessageListResponse;
            }

            String url = externalApiConfig.getChatUrl() + "/messages" +
                    "?user=" + simpleUserInfo.getUserId() +
                    "&conversation_id=" + (StringUtils.isBlank(aiCreationSessionPo.getConversationId()) ? "" : aiCreationSessionPo.getConversationId()) +
                    "&limit=" + limit;

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + externalApiConfig.getChatApiKey())
                    .header("Accept", "application/json")
                    .get()
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }

                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    throw new IOException("Response body is null");
                }

                String responseString = responseBody.string();
                ConversationMessageListResponse parsedObject = JSON.parseObject(responseString, ConversationMessageListResponse.class);
                List<ConversationMessageItem> data = parsedObject.getData();
                if (CollUtil.isEmpty((data))) {
                    ConversationMessageItem conversationMessageItem = new ConversationMessageItem();
                    conversationMessageItem.setQuery(aiCreationSessionPo.getPrompt());
                    List<ConversationMessageItem> conversationMessageItems = Lists.newArrayList(conversationMessageItem);
                    parsedObject.setData(conversationMessageItems);
                }
                return parsedObject;
            }
        } catch (Exception e) {
            log.error("Error fetching conversation messages: {}", e.getMessage(), e);
            throw new BizException("Failed to fetch conversation messages");
        }
    }

    @Override
    public PageResponse<AiCreationSessionVO> getAiCreationSessionsByUserId(AiCreationSessionQueryReq queryReq) {
        SimpleUserInfo simpleUserInfo = UserContext.getUser();
        if (simpleUserInfo == null || StringUtils.isBlank(simpleUserInfo.getUserId())) {
            log.warn("Unable to get user info from UserContext or userId is blank.");
            return PageResponse.of(Collections.emptyList(), 0, queryReq.getPageSize(), queryReq.getPageNum());
        }
        String userId = simpleUserInfo.getUserId();
        log.info("Fetching AI creation sessions for current userId: {}, pageNum: {}, pageSize: {}, status: {}",
                userId, queryReq.getPageNum(), queryReq.getPageSize(), queryReq.getStatus());

        try {
            // 创建分页对象
            Page<AiCreationSessionPo> page = new Page<>(queryReq.getPageNum(), queryReq.getPageSize());

            // 构建查询条件
            LambdaQueryWrapper<AiCreationSessionPo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiCreationSessionPo::getUserId, userId)
                    .eq(AiCreationSessionPo::getDelFlag, 0);

            // 添加状态查询条件（如果提供了状态参数）
            if (queryReq.getStatus() != null) {
                queryWrapper.eq(AiCreationSessionPo::getStatus, queryReq.getStatus());
            }

            // 按创建时间降序排序
            queryWrapper.orderByDesc(AiCreationSessionPo::getCreateTime);

            // 执行分页查询
            Page<AiCreationSessionPo> poPage = aiCreationSessionMapper.selectPage(page, queryWrapper);

            // 转换结果为VO列表
            List<AiCreationSessionVO> voList = poPage.getRecords().stream().map(po -> {
                AiCreationSessionVO vo = new AiCreationSessionVO();
                vo.setSessionId(po.getSessionId()); // Use sessionId from PO
                vo.setConversationId(po.getConversationId());

                AiChapterPo aiChapterPo = aiChapterMapper.selectOne(new LambdaQueryWrapper<AiChapterPo>()
                        .eq(AiChapterPo::getSessionId, po.getSessionId())
                        .orderByDesc(AiChapterPo::getCreateTime)
                        .last("limit 1"));
                if (null != aiChapterPo) {
                    vo.setTitle(aiChapterPo.getSegmentName());
                }else {
                    vo.setTitle("未命名故事");
                }
                vo.setPrompt(po.getPrompt());
                vo.setSoundId(po.getSoundId());
                vo.setImageSize(po.getImageSize());

                vo.setImageUrl(MediaUrlPrefixUtil.getMediaUrl(po.getImageUrl()));
                AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(new LambdaQueryWrapper<AiImageTaskQueuePo>()
                        .eq(AiImageTaskQueuePo::getSessionId, po.getSessionId())
                        .eq(AiImageTaskQueuePo::getContentType, 4)
                        .orderByAsc(AiImageTaskQueuePo::getCreateTime)
                        .last("limit 1"));
                if (null != aiImageTaskQueuePo) {
                    vo.setImageUrl(MediaUrlPrefixUtil.getMediaUrl(aiImageTaskQueuePo.getImageResult()));
                }
                Long count = aiChapterMapper.selectCount(new LambdaQueryWrapper<AiChapterPo>()
                        .eq(AiChapterPo::getSessionId, po.getSessionId()));
                vo.setChapterCount(count.intValue());
                vo.setStatus(po.getStatus());
                vo.setTokenUsage(po.getTokenUsage());
                vo.setCreateTime(po.getCreateTime());
                vo.setUpdateTime(po.getUpdateTime());
                return vo;
            }).collect(Collectors.toList());

            // 返回分页响应
            return PageResponse.of(voList, (int) poPage.getTotal(), queryReq.getPageSize(), queryReq.getPageNum());
        } catch (Exception e) {
            log.error("Error fetching AI creation sessions for userId {}: {}", userId, e.getMessage(), e);
            throw new BizException("Failed to fetch AI creation sessions");
        }
    }

    @Override
    public boolean stopResponse(String conversationId) {
        log.info("stopResponse called with conversationId={}", conversationId);
        try {
            // 根据conversationId查询AiCreationSessionPo获取taskId
            AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(
                    new LambdaQueryWrapper<AiCreationSessionPo>()
                            .eq(AiCreationSessionPo::getSessionId, conversationId)
                            .eq(AiCreationSessionPo::getUserId, UserContext.getUser().getUserId()));

            if (aiCreationSessionPo == null || StringUtils.isBlank(aiCreationSessionPo.getTaskId())) {
                log.error("stopResponse failed: session not found or taskId is blank for conversationId={}", conversationId);
                return false;
            }

            // 构建请求URL和请求体
            String url = externalApiConfig.getChatUrl() + "/chat-messages/" + aiCreationSessionPo.getTaskId() + "/stop";
            JSONObject requestBody = new JSONObject();
            requestBody.put("user", UserContext.getUser().getUserId());

            // 构建请求
            okhttp3.RequestBody body = okhttp3.RequestBody.create(
                    okhttp3.MediaType.parse("application/json; charset=utf-8"),
                    requestBody.toJSONString());

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + externalApiConfig.getChatApiKey())
                    .header("Content-Type", "application/json")
                    .post(body)
                    .build();

            // 执行请求
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("Failed to stop response from external API: {}", response);
                    return false;
                }

                ResponseBody responseBody = response.body();
                if (responseBody != null) {
                    String responseString = responseBody.string();
                    log.info("Stop response API result: {}", responseString);
                    JSONObject jsonResponse = JSON.parseObject(responseString);
                    return "success".equals(jsonResponse.getString("result"));
                }
            }

            return false;
        } catch (Exception e) {
            log.error("Error stopping response: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean clearContext(String conversationId) {
        log.info("clearContext called with conversationId={}", conversationId);
        try {
            // 根据conversationId查询AiCreationSessionPo
            AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(
                    new LambdaQueryWrapper<AiCreationSessionPo>()
                            .eq(AiCreationSessionPo::getSessionId, conversationId)
                            .eq(AiCreationSessionPo::getUserId, UserContext.getUser().getUserId()));

            if (aiCreationSessionPo == null) {
                log.error("clearContext failed: session not found for conversationId={}", conversationId);
                return false;
            }
            AiCreationSessionPo aiCreationSessionPo1 = new AiCreationSessionPo();
            aiCreationSessionPo1.setId(aiCreationSessionPo.getId());
            // 更新conversationId字段为空字符串
            aiCreationSessionPo1.setConversationId("");
            aiCreationSessionPo1.setUpdateTime(new Date());

            // 保存更新后的记录
            int result = aiCreationSessionMapper.updateById(aiCreationSessionPo1);

            // 删除Redis缓存
            redissonClient.getBucket(RedisKeyConstant.Chat.DIFY_CONVERSATION + conversationId).delete();

            return result > 0;
        } catch (Exception e) {
            log.error("Error clearing context: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public SingleResponse<AiCreationSessionInfoVo> getSessionInfoById(String conversationId) {
        log.info("获取会话信息: conversationId={}", conversationId);

        if (StringUtils.isBlank(conversationId)) {
            log.warn("会话ID为空");
            throw new BizException("会话ID为空");
        }

        // 获取当前用户信息
        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || StringUtils.isBlank(userInfo.getUserId())) {
            log.warn("无法获取当前用户信息");
            throw new BizException("无法获取当前用户信息");
        }

        // 查询会话信息
        AiCreationSessionPo sessionPo = aiCreationSessionMapper.selectOne(
                new LambdaQueryWrapper<AiCreationSessionPo>()
                        .eq(AiCreationSessionPo::getSessionId, conversationId)
                        .eq(AiCreationSessionPo::getUserId, userInfo.getUserId())
        );

        if (sessionPo == null) {
            log.warn("未找到会话信息: conversationId={}, userId={}", conversationId, userInfo.getUserId());
            throw new BizException("未找到会话信息");
        }

        // 转换为VO对象
        AiCreationSessionInfoVo sessionVO = new AiCreationSessionInfoVo();
        sessionVO.setConversationId(sessionPo.getSessionId());
        sessionVO.setPrompt(sessionPo.getPrompt());
        sessionVO.setSoundId(sessionPo.getSoundId());
        AgentSoundI18nPo agentSoundI18nPo = agentSoundI18nMapper.selectOne(new LambdaQueryWrapper<AgentSoundI18nPo>()
                .eq(AgentSoundI18nPo::getSoundId, sessionPo.getSoundId())
                .eq(AgentSoundI18nPo::getLocale, "zh_CN"));
        if (agentSoundI18nPo != null) {
            sessionVO.setSoundName(agentSoundI18nPo.getName());
        }
        sessionVO.setImageStyleId(sessionPo.getImageStyleId());
        AiImageStylePo aiImageStylePo = aiImageStyleMapper.selectById(sessionPo.getImageStyleId());
        if (aiImageStylePo != null) {
            sessionVO.setImageStyleName(aiImageStylePo.getStyleName());
        }
        sessionVO.setImageSize(sessionPo.getImageSize());

        AiCreationContentPo aiCreationContentPo = aiCreationContentMapper.selectOne(new LambdaQueryWrapper<AiCreationContentPo>().eq(AiCreationContentPo::getSessionId, conversationId)
                .eq(AiCreationContentPo::getContentType, 10)
                .last("Limit 1"));
        if (aiCreationContentPo != null) {
            List<DesignSaveReq> designSaveReqs = JSON.parseArray(aiCreationContentPo.getContentData(), DesignSaveReq.class);
            sessionVO.setDesignList(designSaveReqs);
        }
        List<AiSessionImagePo> aiSessionImagePos = aiSessionImageMapper.selectList(new LambdaQueryWrapper<AiSessionImagePo>()
                .eq(AiSessionImagePo::getSessionId, conversationId));
        if (CollUtil.isNotEmpty(aiSessionImagePos)) {
            List<String> collect = aiSessionImagePos.stream().map(aiSessionImagePo -> MediaUrlPrefixUtil.getMediaUrl(aiSessionImagePo.getImageUrl())).collect(Collectors.toList());
            sessionVO.setImageList(collect);
        }
        log.info("成功获取会话信息: conversationId={}", conversationId);
        return SingleResponse.of(sessionVO);
    }

    @Override
    public SingleResponse<AiCreationSessionInfoVo> getPublicSessionInfoById(String conversationId) {
        log.info("获取公开会话信息: conversationId={}", conversationId);

        if (StringUtils.isBlank(conversationId)) {
            log.warn("会话ID为空");
            throw new BizException("会话ID为空");
        }

        // 查询会话信息，无需验证用户身份
        AiCreationSessionPo sessionPo = aiCreationSessionMapper.selectOne(
                new LambdaQueryWrapper<AiCreationSessionPo>()
                        .eq(AiCreationSessionPo::getSessionId, conversationId)
        );

        if (sessionPo == null) {
            log.warn("未找到会话信息: conversationId={}", conversationId);
            throw new BizException("未找到会话信息");
        }

        // 转换为VO对象
        AiCreationSessionInfoVo sessionVO = new AiCreationSessionInfoVo();
        sessionVO.setConversationId(sessionPo.getSessionId());
        sessionVO.setPrompt(sessionPo.getPrompt());
        sessionVO.setSoundId(sessionPo.getSoundId());

        // 查询音色信息
        if (sessionPo.getSoundId() != null) {
            AgentSoundI18nPo agentSoundI18nPo = agentSoundI18nMapper.selectOne(new LambdaQueryWrapper<AgentSoundI18nPo>()
                    .eq(AgentSoundI18nPo::getSoundId, sessionPo.getSoundId())
                    .eq(AgentSoundI18nPo::getLocale, "zh_CN"));
            if (agentSoundI18nPo != null) {
                sessionVO.setSoundName(agentSoundI18nPo.getName());
            }
        }

        // 查询图片风格信息
        sessionVO.setImageStyleId(sessionPo.getImageStyleId());
        if (sessionPo.getImageStyleId() != null) {
            AiImageStylePo aiImageStylePo = aiImageStyleMapper.selectById(sessionPo.getImageStyleId());
            if (aiImageStylePo != null) {
                sessionVO.setImageStyleName(aiImageStylePo.getStyleName());
            }
        }

        sessionVO.setImageSize(sessionPo.getImageSize());

        List<AiChapterPo> aiChapterPos = aiChapterMapper.selectList(new LambdaQueryWrapper<AiChapterPo>()
                .eq(AiChapterPo::getSessionId, conversationId));
        if (CollUtil.isNotEmpty(aiChapterPos)) {
            List<AiCreationSessionInfoVo.ChapterPrompt> collect = aiChapterPos.stream().map(aiChapterPo -> {
                AiCreationSessionInfoVo.ChapterPrompt chapterPrompt = new AiCreationSessionInfoVo.ChapterPrompt();
                chapterPrompt.setSegmentId(aiChapterPo.getSegmentId());
                chapterPrompt.setPrompt(aiChapterPo.getPrompt());
                return chapterPrompt;
            }).collect(Collectors.toList());
            sessionVO.setChapterPromptList(collect);
        }

        List<AiSessionImagePo> aiSessionImagePos = aiSessionImageMapper.selectList(new LambdaQueryWrapper<AiSessionImagePo>()
                .eq(AiSessionImagePo::getSessionId, conversationId));
        if (CollUtil.isNotEmpty(aiSessionImagePos)) {
            List<String> collect = aiSessionImagePos.stream().map(aiSessionImagePo -> MediaUrlPrefixUtil.getMediaUrl(aiSessionImagePo.getImageUrl())).collect(Collectors.toList());
            sessionVO.setImageList(collect);
        }
        log.info("成功获取公开会话信息: conversationId={}", conversationId);
        return SingleResponse.of(sessionVO);
    }

    @Override
    public com.alibaba.cola.dto.Response updateSession(UpdateSessionReq request) {
        log.info("更新会话信息: request={}", JSON.toJSONString(request));

        if (StringUtils.isBlank(request.getConversationId())) {
            log.warn("会话ID为空");
            throw new BizException("会话ID不能为空");
        }

        // 获取当前用户信息
        SimpleUserInfo userInfo = UserContext.getUser();
        if (userInfo == null || StringUtils.isBlank(userInfo.getUserId())) {
            log.warn("无法获取当前用户信息");
            throw new BizException("无法获取当前用户信息");
        }

        // 查询会话信息
        AiCreationSessionPo sessionPo = aiCreationSessionMapper.selectOne(
                new LambdaQueryWrapper<AiCreationSessionPo>()
                        .eq(AiCreationSessionPo::getSessionId, request.getConversationId())
                        .eq(AiCreationSessionPo::getUserId, userInfo.getUserId())
        );

        if (sessionPo == null) {
            log.warn("未找到会话信息: conversationId={}, userId={}", request.getConversationId(), userInfo.getUserId());
            throw new BizException("未找到会话信息");
        }

        // 创建更新对象
        AiCreationSessionPo updatePo = new AiCreationSessionPo();
        updatePo.setId(sessionPo.getId());

        // 只更新提供的字段
        boolean hasUpdate = false;

        if (request.getSoundId() != null) {
            updatePo.setSoundId(request.getSoundId());
            hasUpdate = true;
        }

        if (request.getImageStyleId() != null) {
            updatePo.setImageStyleId(request.getImageStyleId());
            hasUpdate = true;
        }

        if (StringUtils.isNotBlank(request.getImageSize())) {
            updatePo.setImageSize(request.getImageSize());
            hasUpdate = true;
        }

        if (!hasUpdate) {
            log.warn("没有提供任何要更新的字段");
            return SingleResponse.buildSuccess();
        }

        // 设置更新时间
        updatePo.setUpdateTime(new Date());

        // 执行更新
        int result = aiCreationSessionMapper.updateById(updatePo);

        if (result > 0) {
            log.info("成功更新会话信息: conversationId={}", request.getConversationId());
            return SingleResponse.buildSuccess();
        } else {
            log.error("更新会话信息失败: conversationId={}", request.getConversationId());
            throw new BizException("更新会话信息失败");
        }
    }

    @Override
    public com.alibaba.cola.dto.Response updatePublicSession(UpdateSessionReq request) {
        log.info("公开接口：更新会话信息: request={}", JSON.toJSONString(request));

        if (StringUtils.isBlank(request.getConversationId())) {
            log.warn("会话ID为空");
            throw new BizException("会话ID不能为空");
        }

        // 查询会话信息，无需验证用户身份
        AiCreationSessionPo sessionPo = aiCreationSessionMapper.selectOne(
                new LambdaQueryWrapper<AiCreationSessionPo>()
                        .eq(AiCreationSessionPo::getSessionId, request.getConversationId())
        );

        if (sessionPo == null) {
            log.warn("未找到会话信息: conversationId={}", request.getConversationId());
            throw new BizException("未找到会话信息");
        }

        // 创建更新对象
        AiCreationSessionPo updatePo = new AiCreationSessionPo();
        updatePo.setId(sessionPo.getId());

        // 只更新提供的字段
        boolean hasUpdate = false;

        if (request.getSoundId() != null) {
            updatePo.setSoundId(request.getSoundId());
            hasUpdate = true;
        }

        if (request.getImageStyleId() != null) {
            updatePo.setImageStyleId(request.getImageStyleId());
            hasUpdate = true;
        }

        if (StringUtils.isNotBlank(request.getImageSize())) {
            updatePo.setImageSize(request.getImageSize());
            hasUpdate = true;
        }

        if (!hasUpdate) {
            log.warn("没有提供任何要更新的字段");
            return SingleResponse.buildSuccess();
        }

        // 设置更新时间
        updatePo.setUpdateTime(new Date());

        // 执行更新
        aiCreationSessionMapper.updateById(updatePo);
        if (null != request.getChapterPrompt()) {
            UpdateSessionReq.ChapterPrompt chapterPrompt = request.getChapterPrompt();
            if (StringUtils.isNotBlank(chapterPrompt.getSegmentId())) {
                AiChapterPo aiChapterPo = aiChapterMapper.selectOne(new LambdaQueryWrapper<AiChapterPo>()
                        .eq(AiChapterPo::getSessionId, request.getConversationId())
                        .eq(AiChapterPo::getSegmentId, chapterPrompt.getSegmentId())
                        .last("limit 1"));
                if (null != aiChapterPo) {
                    aiChapterPo.setPrompt(chapterPrompt.getPrompt());
                    aiChapterPo.setUpdateTime(new Date());
                    aiChapterMapper.updateById(aiChapterPo);
                } else {
                    AiChapterPo chapterPo = new AiChapterPo();
                    chapterPo.setSessionId(request.getConversationId());
                    chapterPo.setSegmentId(chapterPrompt.getSegmentId());
                    chapterPo.setPrompt(chapterPrompt.getPrompt());
                    chapterPo.setCreateTime(new Date());
                    chapterPo.setUpdateTime(new Date());
                    aiChapterMapper.insert(chapterPo);
                }
            }
        }
        return SingleResponse.buildSuccess();
    }

    /**
     * 启动SSE心跳任务
     *
     * @param emitter          SSE发射器
     * @param heartbeatTaskRef 心跳任务引用
     */
    private void startHeartbeat(SseEmitter emitter, AtomicReference<ScheduledFuture<?>> heartbeatTaskRef) {
        // 创建并启动定期发送心跳的任务
        ScheduledFuture<?> task = heartbeatScheduler.scheduleAtFixedRate(() -> {
            try {
                // 创建心跳消息
                JSONObject heartbeat = new JSONObject();
                heartbeat.put("event", "heartbeat");
                heartbeat.put("timestamp", System.currentTimeMillis());

                // 发送心跳消息给客户端
                emitter.send(SseEmitter.event()
                        .name("heartbeat")
                        .data(heartbeat.toJSONString(), MediaType.APPLICATION_JSON));

                log.debug("Sent heartbeat to client");
            } catch (Exception e) {
                log.warn("Failed to send heartbeat: {}", e.getMessage());
                // 如果发送失败，取消心跳任务
                stopHeartbeat(heartbeatTaskRef.get());
            }
        }, heartbeatInterval, heartbeatInterval, TimeUnit.SECONDS);

        // 保存任务引用以便后续取消
        heartbeatTaskRef.set(task);
    }

    /**
     * 停止SSE心跳任务
     *
     * @param heartbeatTask 心跳任务
     */
    private void stopHeartbeat(ScheduledFuture<?> heartbeatTask) {
        if (heartbeatTask != null && !heartbeatTask.isCancelled()) {
            heartbeatTask.cancel(false);
            log.debug("Heartbeat task cancelled");
        }
    }

    /**
     * 清理ChatMessageRequest中的'None'值，防止外部API返回枚举错误
     *
     * @param request 聊天消息请求
     */
    private void sanitizeChatMessageRequest(ChatMessageRequest request) {
        if (request == null) {
            return;
        }

        // 确保response_mode不为null或'None'
        if (StringUtils.isBlank(request.getResponse_mode()) || "None".equals(request.getResponse_mode())) {
            request.setResponse_mode("streaming");
            log.warn("Fixed response_mode from 'None' to 'streaming'");
        }

        // 确保conversation_id不为'None'
        if ("None".equals(request.getConversation_id())) {
            request.setConversation_id("");
            log.warn("Fixed conversation_id from 'None' to empty string");
        }

        // 确保user不为'None'
        if ("None".equals(request.getUser())) {
            request.setUser("");
            log.warn("Fixed user from 'None' to empty string");
        }

        // 确保query不为'None'
        if ("None".equals(request.getQuery())) {
            request.setQuery("");
            log.warn("Fixed query from 'None' to empty string");
        }

        // 检查并修复files字段中的None值
        if (request.getFiles() != null) {
            for (ChatMessageRequest.FileInfo fileInfo : request.getFiles()) {
                if (fileInfo != null) {
                    // 确保type不为None
                    if (StringUtils.isBlank(fileInfo.getType()) || "None".equals(fileInfo.getType())) {
                        fileInfo.setType("image");
                        log.warn("Fixed file type from 'None' to 'image'");
                    }
                    // 确保transferMethod不为None
                    if (StringUtils.isBlank(fileInfo.getTransferMethod()) || "None".equals(fileInfo.getTransferMethod())) {
                        fileInfo.setTransferMethod("remote_url");
                        log.warn("Fixed transfer_method from 'None' to 'remote_url'");
                    }
                    // 确保url不为None
                    if ("None".equals(fileInfo.getUrl())) {
                        fileInfo.setUrl("");
                        log.warn("Fixed file url from 'None' to empty string");
                    }
                }
            }
        }

        log.debug("ChatMessageRequest sanitization completed");
    }

    /**
     * 构建安全的JSON请求体，确保不包含'None'值
     *
     * @param request 聊天消息请求
     * @return 安全的JSON字符串
     */
    private String buildSafeJsonRequest(ChatMessageRequest request) {
        try {
            JSONObject jsonRequest = new JSONObject();

            // 安全设置基本字段
            jsonRequest.put("query", safeString(request.getQuery()));
            jsonRequest.put("response_mode", safeString(request.getResponse_mode(), "streaming"));
            jsonRequest.put("conversation_id", safeString(request.getConversation_id(), ""));
            jsonRequest.put("user", safeString(request.getUser()));

            // 处理inputs字段
            if (request.getInputs() != null && !request.getInputs().isEmpty()) {
                jsonRequest.put("inputs", request.getInputs());
            } else {
                jsonRequest.put("inputs", new JSONObject());
            }

            // 处理files字段
            if (request.getFiles() != null && !request.getFiles().isEmpty()) {
                List<JSONObject> filesArray = new ArrayList<>();
                for (ChatMessageRequest.FileInfo file : request.getFiles()) {
                    if (file != null) {
                        JSONObject fileObj = new JSONObject();
                        fileObj.put("type", safeString(file.getType(), "image"));
                        fileObj.put("transfer_method", safeString(file.getTransferMethod(), "remote_url"));
                        fileObj.put("url", safeString(file.getUrl()));
                        filesArray.add(fileObj);
                    }
                }
                if (!filesArray.isEmpty()) {
                    jsonRequest.put("files", filesArray);
                }
            }

            String jsonString = jsonRequest.toJSONString();

            // 最终检查
            if (jsonString.contains("\"None\"") || jsonString.contains("'None'")) {
                log.error("Still found 'None' in constructed JSON: {}", jsonString);
                throw new RuntimeException("Failed to eliminate 'None' values from JSON");
            }

            return jsonString;

        } catch (Exception e) {
            log.error("Failed to build safe JSON request: {}", e.getMessage(), e);
            throw new RuntimeException("JSON construction failed", e);
        }
    }

    /**
     * 安全字符串处理，将'None'和null转换为空字符串
     */
    private String safeString(String value) {
        return safeString(value, "");
    }

    /**
     * 安全字符串处理，将'None'和null转换为默认值
     */
    private String safeString(String value, String defaultValue) {
        if (value == null || "None".equals(value) || "null".equals(value)) {
            return defaultValue;
        }
        return value;
    }

    @Override
    public PromptOptimizeRes optimizePrompt(String prompt) {
        return optimizePrompt(prompt, null);
    }

    @Override
    public PromptOptimizeRes optimizePrompt(String prompt, List<String> imageUrls) {
        String user = UserContext.getUser().getUserId();
        try {
            log.info("调用Dify提示词优化: prompt={}, user={}, imageUrls={}", prompt, user, imageUrls);
            var response = difyCompletionClient.requestCompletion(prompt, user, imageUrls);

            if (response != null && response.getAnswer() != null) {
                log.info("Dify提示词优化成功: user={}, optimizedPrompt={}", user, response.getAnswer());
                return PromptOptimizeRes.of(response.getAnswer());
            } else {
                log.warn("Dify提示词优化返回空结果: prompt={}, user={}, imageUrls={}", prompt, user, imageUrls);
                throw new BizException("提示词优化失败,请重试");
            }
        } catch (Exception e) {
            log.error("调用Dify提示词优化失败: prompt={}, user={}, imageUrls={}, error={}", prompt, user, imageUrls, e.getMessage(), e);
            throw new BizException("提示词优化失败,请重试");
        }
    }
}