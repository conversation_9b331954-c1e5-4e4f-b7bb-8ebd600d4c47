package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.annotation.IgnoreRequestUser;
import com.wlink.agent.annotation.LogRequest;
import com.wlink.agent.model.req.ContentUpdateReq;
import com.wlink.agent.model.req.DesignSaveReq;
import com.wlink.agent.model.req.NarrationSaveReq;
import com.wlink.agent.model.req.RoleSaveReq;
import com.wlink.agent.model.req.SceneSaveReq;
import com.wlink.agent.model.req.SessionSoundQueryReq;
import com.wlink.agent.model.req.ShotSaveReq;
import com.wlink.agent.model.req.SoundQueryReq;
import com.wlink.agent.model.req.StorySaveReq;
import com.wlink.agent.model.req.TaskProgressSaveReq;
import com.wlink.agent.model.req.TtsGenerateReq;
import com.wlink.agent.model.req.VisualSaveReq;
import com.wlink.agent.model.res.SessionReferenceImageRes;
import com.wlink.agent.model.res.ShotAudioUpdateRes;
import com.wlink.agent.model.res.ShotTaskStatusGroupsRes;
import com.wlink.agent.model.res.ShotTaskStatusRes;
import com.wlink.agent.model.res.SoundRes;
import com.wlink.agent.model.res.TtsGenerateRes;
import com.wlink.agent.service.AgentSoundService;
import com.wlink.agent.service.AiCreationContentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * AI创作内容控制
 */
@Slf4j
@RestController
@RequestMapping("/agent/ai-creation")
@Tag(name = "AI创作内容")
public class AiCreationContentController {

    @Resource
    private AiCreationContentService aiCreationContentService;

    @Resource
    private AgentSoundService agentSoundService;

    /**
     * 处理故事内容操作 (新增/更新/删除)
     *
     * @param conversationId 会话ID
     * @param operationType  操作类型 (add, update, delete)
     * @param storyData      故事数据 (对于 delete 操作，可能只需ID)
     * @return 操作结果
     */
    @LogRequest(description = "处理故事内容操作")
    @IgnoreRequestUser
    @PostMapping("/save/story/{conversationId}/{operationType}") // 修改路径，添operationType
    @Operation(summary = "处理故事内容操作", description = "处理AI创作的故事内容的新增、更新或删除") // 修改描述
    public Response handleStoryOperation( // 修改方法                                          @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
                                          @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
                                          @Parameter(description = "操作类型 (add, update, delete)", required = true, example = "add") @PathVariable String operationType, // 添加 operationType 参数
                                          @Parameter(description = "故事数据", required = true) @RequestBody StorySaveReq storyData) { // data 可能需要根据操作调
        log.info("Handling story operation: type={}, conversationId={}", operationType, conversationId);
        switch (operationType.toLowerCase()) {
            case "add":
                // 调用 Service 层的新增逻辑 (沿用旧方法名，但内部逻辑需区分)
                aiCreationContentService.saveStory(conversationId, storyData);
                log.warn("Service method 'saveStory' (for add) called. Ensure implementation handles add.");
                break;
            case "update":
                // 调用 Service 层的更新逻辑
                aiCreationContentService.updateStory(conversationId, storyData); // 需Service 实现
                break;
            case "delete":
                // 调用 Service 层的删除逻辑
                aiCreationContentService.deleteStory(conversationId, storyData); // 可能只需ID，需Service 实现
                break;
            default:
                log.error("Unsupported operation type for story: {}", operationType);
                return SingleResponse.buildFailure("INVALID_OPERATION", "不支持的操作类型: " + operationType);
        }
        return SingleResponse.buildSuccess();
    }

    /**
     * 处理场景内容操作 (新增/更新/删除)
     *
     * @param conversationId 会话ID
     * @param operationType  操作类型 (add, update, delete)
     * @param sceneData      场景数据
     * @return 操作结果
     */
    @LogRequest(description = "处理场景内容操作")
    @IgnoreRequestUser
    @PostMapping("/save/scene/{conversationId}/{operationType}") // 修改路径
    @Operation(summary = "处理场景内容操作", description = "处理AI创作的场景内容的新增、更新或删除") // 修改描述
    public Response handleSceneOperation( // 修改方法                                          @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
                                          @Parameter(description = "会话ID", required = true)
                                          @PathVariable String conversationId,
                                          @Parameter(description = "操作类型 (add, update, delete)", required = true, example = "add") @PathVariable
                                          String operationType,
                                          @Parameter(description = "场景数据", required = true) @RequestBody SceneSaveReq sceneData) {

        log.info("Handling scene operation: type={}, conversationId={}", operationType, conversationId);
        switch (operationType.toLowerCase()) {
            case "add":
                aiCreationContentService.saveScene(conversationId, sceneData);
                log.warn("Service method 'saveScene' (for add) called. Ensure implementation handles add.");
                break;
            case "update":
                aiCreationContentService.updateScene(conversationId, sceneData);
                log.warn("Service method 'updateScene' not yet implemented or called.");
                break;
            case "delete":
                aiCreationContentService.deleteScene(conversationId, sceneData);
                log.warn("Service method 'deleteScene' not yet implemented or called.");
                break;
            default:
                log.error("Unsupported operation type for scene: {}", operationType);
                return SingleResponse.buildFailure("INVALID_OPERATION", "不支持的场景操作类型: " + operationType);
        }
        return SingleResponse.buildSuccess();
    }

    /**
     * 处理角色内容操作 (新增/更新/删除)
     *
     * @param conversationId 会话ID
     * @param operationType  操作类型 (add, update, delete)
     * @param roleData       角色数据
     * @return 操作结果
     */
    @LogRequest(description = "处理角色内容操作")
    @PostMapping("/save/role/{conversationId}/{operationType}")
    @Operation(summary = "处理角色内容操作", description = "处理AI创作的角色内容的新增、更新或删除")
    public Response handleRoleOperation(
            @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
            @Parameter(description = "操作类型 (add, update, delete)", required = true, example = "add")
            @PathVariable String operationType,
            @Parameter(description = "角色数据", required = true) @RequestBody RoleSaveReq roleData) {

        log.info("Handling role operation: type={}, conversationId={}", operationType, conversationId);
        try {
            switch (operationType.toLowerCase()) {
                case "add":
                    aiCreationContentService.saveRole(conversationId, roleData);
                    log.warn("Service method 'saveRole' (for add) called. Ensure implementation handles add.");
                    break;
                case "update":
                    aiCreationContentService.updateRole(conversationId, roleData);
                    log.warn("Service method 'updateRole' not yet implemented or called.");
                    break;
                case "delete":
                    aiCreationContentService.deleteRole(conversationId, roleData);
                    log.warn("Service method 'deleteRole' not yet implemented or called.");
                    break;
                default:
                    log.error("Unsupported operation type for role: {}", operationType);
                    return SingleResponse.buildFailure("INVALID_OPERATION", "不支持的角色操作类型: " + operationType);
            }
            return SingleResponse.buildSuccess();
        } catch (Exception e) {
            log.error("Error handling role operation: type={}, conversationId={}", operationType, conversationId, e);
            return SingleResponse.buildFailure("OPERATION_FAILED", "处理角色操作时发生错 " + e.getMessage());
        }
    }

    /**
     * 处理分镜内容操作 (新增/更新/删除)
     *
     * @param conversationId 会话ID
     * @param operationType  操作类型 (add, update, delete)
     * @param shotData       分镜数据
     * @return 操作结果
     */

    @LogRequest(description = "处理分镜内容操作")
    @PostMapping("/save/shot/{conversationId}/{operationType}") // 修改路径
    @Operation(summary = "处理分镜内容操作", description = "处理AI创作的分镜内容的新增、更新或删除") // 修改描述
    public Response handleShotOperation( // 修改方法                                         @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
                                         @Parameter(description = "操作类型 (add, update, delete)", required = true, example = "add")
                                         @PathVariable String operationType,
                                         @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
                                         @Parameter(description = "分镜数据", required = true) @RequestBody ShotSaveReq shotData) {

        log.info("Handling shot operation: type={}, conversationId={}", operationType, conversationId);
        try {
            switch (operationType.toLowerCase()) {
                case "add":
                    aiCreationContentService.saveShot(conversationId, shotData);
                    log.warn("Service method 'saveShot' (for add) called. Ensure implementation handles add.");
                    break;
                case "update":
                    aiCreationContentService.updateShot(conversationId, shotData);
                    log.warn("Service method 'updateShot' not yet implemented or called.");
                    break;
                case "delete":
                    aiCreationContentService.deleteShot(conversationId, shotData);
                    log.warn("Service method 'deleteShot' not yet implemented or called.");
                    break;
                default:
                    log.error("Unsupported operation type for shot: {}", operationType);
                    return SingleResponse.buildFailure("INVALID_OPERATION", "不支持的分镜操作类型: " + operationType);
            }
            return SingleResponse.buildSuccess();
        } catch (Exception e) {
            log.error("Error handling shot operation: type={}, conversationId={}", operationType, conversationId, e);
            return SingleResponse.buildFailure("OPERATION_FAILED", "处理分镜操作时发生错 " + e.getMessage());
        }
    }


    /**
     * 处理故事设计操作 (新增/更新/删除)
     *
     * @param conversationId 会话ID
     * @param operationType  操作类型 (add, update, delete)
     * @param shotData       分镜数据
     * @return 操作结果
     */
    @LogRequest(description = "处理故事设计操作")
    @PostMapping("/save/design/{conversationId}/{operationType}") // 修改路径
    @Operation(summary = "Operation")
    public Response handleDesignOperation( // 修改方法
                                           @Parameter(description = "操作类型 (add, update, delete)", required = true, example = "add")
                                           @PathVariable String operationType,
                                           @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
                                           @Parameter(description = "故事设计数据", required = true) @RequestBody DesignSaveReq shotData) {

        log.info("Handling design operation: type={}, conversationId={}", operationType, conversationId);
        switch (operationType.toLowerCase()) {
            case "add":
                aiCreationContentService.saveDesign(conversationId, shotData);
                log.warn("Service method 'saveDesign' (for add) called. Ensure implementation handles add.");
                break;
            case "update":
                aiCreationContentService.saveDesign(conversationId, shotData);
                log.warn("Service method 'updateDesign' not yet implemented or called.");
                break;
            default:
                log.error("Unsupported operation type for design: {}", operationType);
                return SingleResponse.buildFailure("INVALID_OPERATION", "不支持的故事设计操作类型: " + operationType);
        }
        return SingleResponse.buildSuccess();

    }

    /**
     * 保存故事旁白
     *
     * @param conversationId 会话ID
     * @param operationType  操作类型 (add, update, delete)
     * @param narrationData  故事旁白数据
     * @return 操作结果
     */
    @LogRequest(description = "处理故事旁白操作")
    @PostMapping("/save/narration/{conversationId}/{operationType}")
    @Operation(summary = "处理故事旁白操作", description = "处理AI创作的故事旁白的新增、更新或删除")
    public Response handleNarrationOperation(
            @Parameter(description = "操作类型 (add, update, delete)", required = true, example = "add")
            @PathVariable String operationType,
            @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
            @Parameter(description = "故事旁白数据", required = true) @RequestBody List<NarrationSaveReq> narrationData) {

        log.info("Handling narration operation: type={}, conversationId={}", operationType, conversationId);
        try {
            switch (operationType.toLowerCase()) {
                case "add":
                    aiCreationContentService.saveNarration(conversationId, narrationData);
                    log.warn("Service method 'saveNarration' (for add) called. Ensure implementation handles add.");
                    break;
                case "update":
                    aiCreationContentService.updateNarration(conversationId, narrationData);
                    log.warn("Service method 'updateNarration' not yet implemented or called.");
                    break;
                case "delete":
                    aiCreationContentService.deleteNarration(conversationId, narrationData);
                    log.warn("Service method 'deleteNarration' not yet implemented or called.");
                    break;
                default:
                    log.error("Unsupported operation type for narration: {}", operationType);
                    return SingleResponse.buildFailure("INVALID_OPERATION", "不支持的故事旁白操作类型: " + operationType);
            }
            return SingleResponse.buildSuccess();
        } catch (Exception e) {
            log.error("Error handling narration operation: type={}, conversationId={}", operationType, conversationId, e);
            return SingleResponse.buildFailure("OPERATION_FAILED", "处理故事旁白操作时发生错 " + e.getMessage());
        }
    }


    /**
     * 处理视觉内容操作 (新增/更新/删除)
     *
     * @param conversationId 会话ID
     * @param visualData     视觉数据 (类型VisualSaveReq)
     * @return 操作结果
     */
    @LogRequest(description = "处理视觉内容操作")
    @PostMapping("/save/visual/{conversationId}")
    @Operation(summary = "处理视觉内容操作", description = "处理AI创作的视觉内容的新增、更新或删除")
    public Response handleVisualOperation(
            @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
            @Parameter(description = "视觉数据", required = true) @RequestBody VisualSaveReq visualData) { // 注意类型VisualSaveReq

        aiCreationContentService.saveVisual(conversationId, visualData);
        return SingleResponse.buildSuccess();

    }


    /**
     * 根据会话ID和内容类型查询内容数     *
     *
     * @param conversationId 会话ID
     * @param contentType    内容类型(1-故事,2-场景,3-角色,4-分镜,5-视觉,10-故事设计,11-故事旁白)
     * @return 内容数据
     */
    @LogRequest(description = "查询内容数据")
    @GetMapping("/save/content/query")
    @Operation(summary = "Operation")
    public SingleResponse<Object> queryContentData(
            @Parameter(description = "内容类型") @RequestParam(value = "contentType") Integer contentType,
            @Parameter(description = "会话ID", required = true) @RequestParam(value = "conversationId") String conversationId) {
        // 这里应从安全上下文中获取用户ID，暂时使用固定        log.info("Query content data for conversationId={}, contentType={}", conversationId, contentType);
        Object contentData = aiCreationContentService.getContentDataByDify(conversationId, contentType);
        return SingleResponse.of(contentData);
    }


    @Operation(summary = "查询声音列表")
    @GetMapping("/sound/list")
    public MultiResponse<SoundRes> listSounds(
            @Parameter(description = "声音类型：1-系统 2-定制", required = false) @RequestParam(value = "type", required = false) Integer type,
            @Parameter(description = "语言", required = false) @RequestParam(value = "language", required = false) @Size(max = 50) String language,
            @Parameter(description = "性别：1-男 2-女", required = false) @RequestParam(value = "sex", required = false) Integer sex) {

        // 构SoundQueryReq 对象
        SoundQueryReq req = new SoundQueryReq();
        req.setType(type);
        req.setLanguage(language);
        req.setSex(sex);

        log.info("Received request to list sounds with query: type={}, language='{}', sex={}", type, language, sex);

        // 直接调用服务层方法，由全局异常处理器处理异
        List<SoundRes> sounds = agentSoundService.listSounds(req);
        log.info("Found {} sounds matching the query.", sounds.size());

        // 直接返回成功响应
        return MultiResponse.of(sounds);

    }


    /**
     * 根据会话ID和内容类型查询内容数     *
     *
     * @param conversationId 会话ID
     * @param contentType    内容类型(1-故事,2-场景,3-角色,4-分镜,5-视觉)
     * @return 内容数据
     */
    @GetMapping("/conversation/content")
    @Operation(summary = "根据会话ID和内容类型查询内容")
    public SingleResponse<Object> getContentData(
            @Parameter(description = "内容类型") @RequestParam(value = "contentType") Integer contentType,
            @Parameter(description = "会话ID", required = true) @RequestParam(value = "conversationId") String conversationId) {
        // 这里应从安全上下文中获取用户ID，暂时使用固定        log.info("Query content data for conversationId={}, contentType={}", conversationId, contentType);
        Object contentData = aiCreationContentService.getContentData(conversationId, contentType);
        return SingleResponse.of(contentData);
    }


    /**
     * 生成TTS音频
     *
     * @param req TTS生成请求参数
     * @return 操作结果，包含音频URL
     */
    @LogRequest(description = "生成TTS音频")
    @PostMapping("/tts/generate")
    @Operation(summary = "生成TTS音频", description = "根据文本和声音ID生成音频文件")
    public SingleResponse<TtsGenerateRes> generateTtsAudio(
            @Parameter(description = "TTS生成请求参数", required = true) @Valid @RequestBody TtsGenerateReq req) { // 添加 @Valid 进行参数校验
        log.info("Received request to generate TTS audio: conversationId={}, voiceId={}", req.getConversationId(), req.getVoiceId());
        TtsGenerateRes result = aiCreationContentService.generateTts(req);
        log.info("TTS audio generated successfully: recordId={}, audioUrl={}", result.getRecordId(), result.getAudioUrl());
        return SingleResponse.of(result);

    }

    /**
     * 保存任务进度
     *
     * @param progressReq 任务进度请求参数
     * @return 操作结果
     */
    @LogRequest(description = "保存任务进度") // Assuming no user authentication needed for this endpoint
    @PostMapping("/save/task/progress/{conversationId}")
    @Operation(summary = "Operation")
    public Response saveTaskProgress(
            @Parameter(description = "会话ID", required = true) @PathVariable String conversationId,
            @Parameter(description = "任务进度数据", required = true) @RequestBody TaskProgressSaveReq progressReq) {
        aiCreationContentService.saveTaskProgress(conversationId, progressReq);
        return SingleResponse.buildSuccess();
    }

    /**
     * 查询最新的任务进度
     *
     * @param conversationId 会话ID
     * @return 最新的任务进度数据
     */
    @LogRequest(description = "查询最新的任务进度")
    @GetMapping("/task/progress/{conversationId}")
    @Operation(summary = "Operation")
    public SingleResponse<Object> getLatestTaskProgress(
            @Parameter(description = "会话ID", required = true) @PathVariable String conversationId) {
        Object progressData = aiCreationContentService.getLatestTaskProgress(conversationId);
        return SingleResponse.of(progressData);

    }

    /**
     * 修改会话内容
     *
     * @param req 内容更新请求
     * @return 操作结果，如果是分镜且内容变化，返回更新的音频信
     */
    @PostMapping("/update/content")
    @Operation(summary = "修改会话内容", description = "根据会话ID和内容类型修改会话内容，如果是分镜且内容变化，返回更新的音频信息")
    public MultiResponse<ShotAudioUpdateRes> updateConversationContent(@RequestBody ContentUpdateReq req) {
        log.info("修改会话内容：conversationId={}, contentType={}", req.getConversationId(), req.getContentType());
        List<ShotAudioUpdateRes> updatedAudios = aiCreationContentService.updateConversationContent(req.getConversationId(), req.getContentType(), req.getContentData());
        return MultiResponse.of(updatedAudios);
    }

    /**
     * 查询分镜任务状(已过时，推荐使用 getShotTaskStatusGrouped 方法)
     *
     * @param conversationId 会话ID
     * @param contentId      内容ID
     * @param contentIdType  内容ID类型(1-chapter 2-scenes 3-shot)
     * @return 分镜任务状态列     * @deprecated 此方法已过时，请使用 {@link #getShotTaskStatusGrouped(String, String, Integer)}
     */
    @Deprecated
    @GetMapping("/shot/status")
    @Operation(summary = "查询分镜任务状(已过", description = "此接口已过时，请使用 /shot/status/grouped 接口")
    public MultiResponse<ShotTaskStatusRes> getShotTaskStatus(
            @Parameter(description = "会话ID", required = true) @RequestParam(value = "conversationId") String conversationId,
            @Parameter(description = "内容ID", required = true) @RequestParam(value = "contentId") String contentId,
            @Parameter(description = "内容ID类型") @RequestParam(value = "contentIdType") Integer contentIdType) {

        log.info("查询分镜任务状已过: conversationId={}, contentId={}, contentIdType={}", conversationId, contentId, contentIdType);

        // 调用新的 getShotTaskStatus 方法，然后将结果转换为旧格式
        ShotTaskStatusGroupsRes result = aiCreationContentService.getShotTaskStatus(conversationId, contentId, contentIdType);

        // 将新结果格式转换为旧格式
        List<ShotTaskStatusRes> statusList = convertToOldFormat(result);
        return MultiResponse.

                of(statusList);
    }

    /**
     * 将新格式的分镜任务状态转换为旧格式
     *
     * @param newResult 新格式的分镜任务状态
     * @return 旧格式的分镜任务状态列表
     */
    private List<ShotTaskStatusRes> convertToOldFormat(ShotTaskStatusGroupsRes newResult) {
        List<ShotTaskStatusRes> result = new ArrayList<>();

        if (newResult == null || newResult.getShotGroups() == null) {
            return result;
        }

        // 遍历所有章节
        for (ShotTaskStatusGroupsRes.ChapterGroup chapterGroup : newResult.getShotGroups()) {
            if (chapterGroup.getScenes() == null) {
                continue;
            }

            // 遍历所有场景
            for (ShotTaskStatusGroupsRes.SceneGroup sceneGroup : chapterGroup.getScenes()) {
                String sceneId = sceneGroup.getSceneId();

                if (sceneGroup.getShots() == null) {
                    continue;
                }

                // 遍历所有镜头
                for (ShotTaskStatusGroupsRes.ShotDetail shotDetail : sceneGroup.getShots()) {
                    // 创建旧格式的镜头状态对象
                    ShotTaskStatusRes statusRes = ShotTaskStatusRes.builder()
                            .shotId(shotDetail.getShotId())
                            .sceneId(sceneId)
                            .imageStatus(shotDetail.getImageStatus())
                            .voiceStatus(shotDetail.getVoiceStatus())
                            .narrationStatus(shotDetail.getNarrationStatus())
                            .build();

                    result.add(statusRes);
                }
            }
        }

        return result;
    }


    /**
     * 查询分镜任务状态（分组）
     * 当 contentId 为空时，查询所有分镜任务状态，并按章节和场景分组返回
     * 当 contentId 不为空时，根据 contentId 和 contentIdType 查询特定分镜任务状态
     *
     * @param conversationId 会话ID
     * @param contentId      内容ID，可为空，为空时查询所有分镜任务状态
     * @param contentIdType  内容ID类型(1-segment 2-scenes 3-shot)，当 contentId 为空时此参数无效
     * @return 分组的分镜任务状态
     */
    @IgnoreRequestUser
    @GetMapping("/shot/status/grouped")
    @Operation(summary = "查询分镜任务状态（分组）", description = "根据会话ID、内容ID和内容ID类型查询分镜的图片、声音、旁白的处理状态，按段落和场景分组。当内容ID为空时，返回所有分镜任务状态。")
    public SingleResponse<ShotTaskStatusGroupsRes> getShotTaskStatusGrouped(
            @Parameter(description = "会话ID", required = true) @RequestParam(value = "conversationId") String conversationId,
            @Parameter(description = "内容ID，可为空，为空时查询所有分镜任务状态", required = false) @RequestParam(value = "contentId", required = false) String contentId,
            @Parameter(description = "内容ID类型(1-segment 2-scenes 3-shot)，当contentId为空时此参数无效", required = false, example = "1") @RequestParam(value = "contentIdType", required = false, defaultValue = "1") Integer contentIdType) {

        log.info("查询分镜任务状态(分组): conversationId={}, contentId={}, contentIdType={}", conversationId, contentId, contentIdType);

        // 调用 getShotTaskStatus 方法，返回基于故事大纲的分镜任务状态
        ShotTaskStatusGroupsRes result = aiCreationContentService.getShotTaskStatus(conversationId, contentId, contentIdType);
        return SingleResponse.of(result);
    }

    /**
     * 根据会话ID查询声音列表
     *
     * @param sessionId 会话ID
     * @return 声音列表（包括系统声音和用户定制声音）
     */
    @GetMapping("/sound/session/{sessionId}")
    @Operation(summary = "根据会话ID查询声音列表", description = "根据会话ID查询系统声音和用户定制声音列表")
    public MultiResponse<SoundRes> listSoundsBySession(
            @Parameter(description = "会话ID", required = true) @PathVariable String sessionId) {
        log.info("根据会话ID查询声音列表: sessionId={}", sessionId);

        SessionSoundQueryReq req = new SessionSoundQueryReq();
        req.setSessionId(sessionId);

        List<SoundRes> result = agentSoundService.listSoundsBySession(req);
        return MultiResponse.of(result);
    }
}
