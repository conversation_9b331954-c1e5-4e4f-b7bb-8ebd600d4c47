package com.wlink.agent.client.model.dify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Dify文件信息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DifyFileInfo {
    
    /**
     * 文件类型，默认为image
     */
    @JsonProperty("type")
    @Builder.Default
    private String type = "image";
    
    /**
     * 传输方式，默认为remote_url
     */
    @JsonProperty("transfer_method")
    @Builder.Default
    private String transferMethod = "remote_url";
    
    /**
     * 文件URL
     */
    @JsonProperty("url")
    private String url;
    
    /**
     * 创建图片文件信息的便捷方法
     *
     * @param url 图片URL
     * @return DifyFileInfo对象
     */
    public static DifyFileInfo createImageFile(String url) {
        return DifyFileInfo.builder()
                .type("image")
                .transferMethod("remote_url")
                .url(url)
                .build();
    }
}
