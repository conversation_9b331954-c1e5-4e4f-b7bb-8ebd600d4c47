package com.wlink.agent.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.dify.DifyCompletionInputs;
import com.wlink.agent.client.model.dify.DifyCompletionRequest;
import com.wlink.agent.client.model.dify.DifyCompletionResponse;
import com.wlink.agent.client.model.dify.DifyFileInfo;
import com.wlink.agent.config.DifyConfig;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.config.ExternalApiConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Dify完成消息API客户端
 * 使用OkHttp实现同步HTTP调用
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DifyCompletionClient {

    private final ExternalApiConfig externalApiConfig;
    private final ObjectMapper objectMapper;
    private final OkHttpClient httpClient;

    /**
     * 调用Dify完成消息API
     *
     * @param query 查询内容
     * @param user 用户标识
     * @return Dify响应对象
     * @throws IOException 网络IO异常
     */
    public DifyCompletionResponse requestCompletion(String query, String user) throws IOException {
        return requestCompletion(query, user, null);
    }

    /**
     * 调用Dify完成消息API（支持文件）
     *
     * @param query 查询内容
     * @param user 用户标识
     * @param imageUrls 图片URL列表
     * @return Dify响应对象
     * @throws IOException 网络IO异常
     */
    public DifyCompletionResponse requestCompletion(String query, String user, List<String> imageUrls) throws IOException {
        // 构建请求对象
        DifyCompletionRequest request = buildCompletionRequest(query, user, imageUrls);

        // 调用API
        return requestCompletionWithOkHttp(request);
    }

    /**
     * 使用OkHttp同步请求Dify完成消息API
     *
     * @param request 请求参数
     * @return 响应对象
     * @throws IOException 网络IO异常
     */
    public DifyCompletionResponse requestCompletionWithOkHttp(DifyCompletionRequest request) throws IOException {
        String apiUrl = externalApiConfig.getCompletionUrl();
        log.info("[OkHttp] 发送请求到Dify完成消息API: URL={}, Payload={}", apiUrl, request);
        
        String requestBodyJson;
        try {
            requestBodyJson = objectMapper.writeValueAsString(request);
            log.debug("[OkHttp] 序列化请求体: {}", requestBodyJson);
        } catch (JsonProcessingException e) {
            log.error("[OkHttp] 请求序列化为JSON失败: Request={}, Error={}", request, e.getMessage(), e);
            throw new BizException("序列化请求失败");
        }

        // 创建请求体
        RequestBody requestBody = RequestBody.create(
                requestBodyJson,
                MediaType.get("application/json; charset=utf-8")
        );

        // 构建HTTP请求
        Request httpRequest = new Request.Builder()
                .url(apiUrl)
                .post(requestBody)
                .addHeader("Authorization", "Bearer " + externalApiConfig.getTextOptimizeApiKey())
                .addHeader("Content-Type", "application/json")
                .build();

        // 发送请求并处理响应
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "No response body";
                log.error("[OkHttp] Dify API调用失败: 状态码={}, 响应体={}", response.code(), errorBody);
                throw new BizException("Dify API调用失败，状态码: " + response.code());
            }

            String responseBodyString = response.body().string();
            log.info("[OkHttp] Dify API响应: {}", responseBodyString);

            try {
                DifyCompletionResponse difyResponse = objectMapper.readValue(responseBodyString, DifyCompletionResponse.class);
                log.info("[OkHttp] 成功反序列化Dify响应: {}", difyResponse);
                return difyResponse;
            } catch (JsonProcessingException e) {
                log.error("[OkHttp] 响应反序列化失败: ResponseBody={}, Error={}", responseBodyString, e.getMessage(), e);
                throw new BizException("响应反序列化失败");
            }
        }
    }

    /**
     * 构建Dify完成请求对象
     *
     * @param query 查询内容
     * @param user 用户标识
     * @return Dify完成请求对象
     */
    private DifyCompletionRequest buildCompletionRequest(String query, String user) {
        return buildCompletionRequest(query, user, null);
    }

    /**
     * 构建Dify完成请求对象（支持文件）
     *
     * @param query 查询内容
     * @param user 用户标识
     * @param imageUrls 图片URL列表
     * @return Dify完成请求对象
     */
    private DifyCompletionRequest buildCompletionRequest(String query, String user, List<String> imageUrls) {
        DifyCompletionInputs inputs = DifyCompletionInputs.builder()
                .query(query)
                .build();

        // 构建文件列表
        List<DifyFileInfo> files = null;
        if (imageUrls != null && !imageUrls.isEmpty()) {
            files = imageUrls.stream()
                    .map(DifyFileInfo::createImageFile)
                    .collect(Collectors.toList());
        }

        return DifyCompletionRequest.builder()
                .inputs(inputs)
                .user(user)
                .responseMode("blocking")
                .files(files)
                .build();
    }
}
